<template>
  <div>
    <!--  <PERSON><PERSON>, <PERSON> início, Data fim, Habilitar data fim -->
    <div class="row">
      <div class="col-md-3">
        <div class="form-group">
          <CustomLabel
            required
            text="Nome da turma"
            help="Insira um nome para a turma. Exemplo: Turma ADM 2025."
          />

          <CustomInput
            v-model="localOfferClass.classname"
            placeholder="Digite o nome da turma"
            required
            id="classname"
            :disabled="isReadonly"
            :has-error="formErrors.classname.hasError"
            :error-message="formErrors.classname.message"
            @validate="validateForm()"
          />
        </div>
      </div>
      <div class="col-md-3">
        <div class="form-group">
          <CustomLabel
            required
            text="Data de início"
            help="Insira uma data de início para a turma. Exemplo: 07/04/2025."
          />

          <CustomInput
            v-model="localOfferClass.startdate"
            type="date"
            required
            class="date-input"
            id="startdate"
            :disabled="isReadonly"
            :has-error="formErrors.startdate.hasError"
            :error-message="formErrors.startdate.message"
            @validate="validateForm()"
          />
        </div>
      </div>

      <div class="col-md-3">
        <div
          class="form-group"
          :class="{ disabled: !localOfferClass.optional_fields.enableenddate }"
        >
          <CustomLabel
            text="Data de término"
            help="Insira uma data de término para a turma, caso haja. Exemplo: 07/12/2025."
          />

          <CustomInput
            v-model="localOfferClass.optional_fields.enddate"
            type="date"
            :disabled="
              !localOfferClass.optional_fields.enableenddate || isReadonly
            "
            required
            id="enddate"
            class="date-input"
            :has-error="formErrors.enddate.hasError"
            :error-message="formErrors.enddate.message"
            @validate="validateForm()"
          />
        </div>
      </div>

      <div class="col-md-3 ml-md-n3 mt-n3 mt-md-0">
        <div class="form-group">
          <CustomLabel text="&nbsp" className="d-none d-md-block" />
          <CustomCheckbox
            v-model="localOfferClass.optional_fields.enableenddate"
            id="enableEndDate"
            label="Habilitar data de termínio"
            :disabled="isReadonly"
          />
        </div>
      </div>
    </div>

    <!--Descrição -->
    <div class="row">
      <div class="col-12">
        <div class="form-group">
          <CustomLabel
            text="Descrição da turma"
            help="Esta descrição estará disponível para os usuários na página intermediária do curso. Exemplo: Esta turma se destina a usuários com os cargos administrativos que foram selecionados para a realização dos cursos obrigatórios do ano de 2025"
          />
          <TextEditor
            v-model="localOfferClass.optional_fields.description"
            placeholder="Digite a descrição da turma aqui..."
            :rows="4"
            :disabled="isReadonly"
            @update:modelValue="validateForm()"
          />
        </div>
      </div>
    </div>

    <!-- Pré-inscrição -->
    <div class="row">
      <div class="col-md-3">
        <div
          class="form-group"
          :class="{
            disabled: !localOfferClass.optional_fields.enablepreenrolment,
          }"
        >
          <CustomLabel
            :required="localOfferClass.optional_fields.enablepreenrolment"
            text="Data de início da inscrição"
            help="Data de início do período de inscrição."
          />

          <CustomInput
            v-model="localOfferClass.optional_fields.preenrolmentstartdate"
            type="date"
            :disabled="
              !localOfferClass.optional_fields.enablepreenrolment || isReadonly
            "
            class="date-input"
            :has-error="formErrors.preenrolmentstartdate.hasError"
            :error-message="formErrors.preenrolmentstartdate.message"
            @validate="validateForm()"
          />
        </div>
      </div>

      <div class="col-md-3">
        <div
          class="form-group"
          :class="{
            disabled: !localOfferClass.optional_fields.enablepreenrolment,
          }"
        >
          <CustomLabel
            :required="localOfferClass.optional_fields.enablepreenrolment"
            text="Data de término da inscrição"
          />

          <CustomInput
            v-model="localOfferClass.optional_fields.preenrolmentenddate"
            type="date"
            :disabled="
              !localOfferClass.optional_fields.enablepreenrolment || isReadonly
            "
            class="date-input"
            :has-error="formErrors.preenrolmentenddate.hasError"
            :error-message="formErrors.preenrolmentenddate.message"
            @validate="validateForm()"
          />
        </div>
      </div>

      <div class="col-md-3 ml-md-n3 mt-n3 mt-md-0">
        <div class="form-group">
          <CustomLabel text="&nbsp" className="d-none d-md-block" />
          <CustomCheckbox
            v-model="localOfferClass.optional_fields.enablepreenrolment"
            id="enablePreEnrolment"
            label="Habilitar prazo de inscrição"
            :disabled="isReadonly"
          />
        </div>
      </div>
    </div>

    <!--  Mínimo de usuários, Máximo de usuários, Número máximo de inscrições por concessionária -->
    <div class="row" v-if="localOfferClass.enrol !== 'offer_audience'">
      <div class="col-md-3" v-if="localOfferClass.enrol !== 'offer_audience'">
        <div class="form-group">
          <CustomLabel
            text="Número mínimo de inscrições"
            help="Insira um número mínimo de inscrições (vagas) para a turma, se houver. Exemplo: 20."
          />

          <CustomInput
            v-model="localOfferClass.optional_fields.minusers"
            type="number"
            :has-error="formErrors.minusers.hasError"
            :error-message="formErrors.minusers.message"
            @validate="validateForm()"
            :min="0"
            :disabled="isReadonly"
          />
        </div>
      </div>

      <div class="col-md-3" v-if="localOfferClass.enrol !== 'offer_audience'">
        <div class="form-group">
          <CustomLabel
            text="Número máximo de inscrições"
            help="Insira um número máximo de inscrições (vagas) para a turma. Exemplo: 100."
          />

          <CustomInput
            v-model="localOfferClass.optional_fields.maxusers"
            type="number"
            :has-error="formErrors.maxusers.hasError"
            :error-message="formErrors.maxusers.message"
            @validate="validateForm()"
            :min="0"
            :disabled="isReadonly"
          />
        </div>
      </div>
      <div class="col-md-4">
        <div class="form-group">
          <CustomLabel
            text="Número máximo de inscrições por concessionária"
            help="Insira um número máximo de inscrições por concessionária para a turma, se houver. Exemplo: 5."
          />
          <CustomInput
            v-model="localOfferClass.optional_fields.max_users_company"
            type="number"
            :has-error="formErrors.max_users_company.hasError"
            :error-message="formErrors.max_users_company.message"
            @validate="validateForm()"
            :min="0"
            :disabled="isReadonly"
          />
        </div>
      </div>
    </div>

    <!--Habilitar restrição por estruturas -->
    <div class="row mb-n3">
      <div class="col-md-12">
        <div
          class="form-group"
          :class="{
            disabled:
              !localOfferClass.optional_fields.enablehirearchyrestriction,
            'dependent-field': true,
          }"
        >
          <CustomCheckbox
            v-model="localOfferClass.optional_fields.enablehirearchyrestriction"
            id="enablehirearchyrestriction"
            label="Habilitar restrição por estruturas"
            help="Ao habilitar esta opção, os campos 'Divisão', 'Setor', 'Grupo' e 'Concessionária' serão automaticamente ajustados conforme as seleções realizadas nos respectivos filtros."
            :disabled="isReadonly"
          />
        </div>
      </div>
    </div>

    <!-- Restrição por Divisão, Setor, Grupo, Concessionária -->
    <div class="row">
      <div class="col-md-4 col-lg-3">
        <div class="form-group">
          <CustomLabel text="Divisão" />
          <Autocomplete
            class="autocomplete-audiences"
            v-model="
              localOfferClass.optional_fields.hirearchyrestrictiondivisions
            "
            :items="divisionOptions"
            placeholder="Selecionar"
            :required="true"
            :autoOpen="false"
            :show-all-option="true"
            :disabled="
              !localOfferClass.optional_fields.enablehirearchyrestriction ||
              isReadonly
            "
            @update:modelValue="validateForm()"
          />
        </div>
      </div>
      <div class="col-md-4 col-lg-3">
        <div class="form-group">
          <CustomLabel text="Setor" />
          <Autocomplete
            class="autocomplete-audiences"
            v-model="
              localOfferClass.optional_fields.hirearchyrestrictionsectors
            "
            :items="sectorOptions"
            placeholder="Selecionar"
            :required="true"
            :autoOpen="false"
            :show-all-option="true"
            :disabled="
              !localOfferClass.optional_fields.enablehirearchyrestriction ||
              !localOfferClass.optional_fields.hirearchyrestrictiondivisions
                .length ||
              isReadonly
            "
            @update:modelValue="validateForm()"
          />
        </div>
      </div>
      <div class="col-md-4 col-lg-3">
        <div class="form-group">
          <CustomLabel text="Grupo" />
          <Autocomplete
            class="autocomplete-audiences"
            v-model="localOfferClass.optional_fields.hirearchyrestrictiongroups"
            :items="groupOptions"
            placeholder="Selecionar"
            :required="true"
            :autoOpen="false"
            :show-all-option="true"
            :disabled="
              !localOfferClass.optional_fields.enablehirearchyrestriction ||
              !localOfferClass.optional_fields.hirearchyrestrictionsectors
                .length ||
              isReadonly
            "
            @update:modelValue="validateForm()"
          />
        </div>
      </div>
      <div class="col-md-4 col-lg-3">
        <div class="form-group">
          <CustomLabel text="Concessionária" />
          <Autocomplete
            class="autocomplete-audiences"
            v-model="
              localOfferClass.optional_fields.hirearchyrestrictiondealerships
            "
            :items="dealershipOptions"
            placeholder="Selecionar"
            :required="true"
            :autoOpen="false"
            :show-all-option="true"
            :disabled="
              !localOfferClass.optional_fields.enablehirearchyrestriction ||
              !localOfferClass.optional_fields.hirearchyrestrictiongroups
                .length ||
              isReadonly
            "
            @update:modelValue="validateForm()"
          />
        </div>
      </div>
    </div>

    <!-- Perfil atribuído por padrão, Modalidade, Duração da matrícula -->
    <div class="row">
      <div class="col-md-3">
        <div class="form-group">
          <CustomLabel
            required
            text="Perfil atribuído por padrão"
            help="Caso a turma seja criada para perfis de 'Aluno Nissan' e ‘Aluno Concessionária’, o perfil padrão selecionado será ‘Estudante’.<br><br>
                    Caso a turma seja criada para perfis de ‘Gestores’, o perfil padrão selecionado será ‘Gestor’ ou outro perfil pertinente."
          />

          <CustomSelect
            v-model="localOfferClass.optional_fields.roleid"
            :options="roleOptions"
            required
            :disabled="isReadonly"
            :has-error="formErrors.roleid.hasError"
            :error-message="formErrors.roleid.message"
            @validate="validateForm()"
          />
        </div>
      </div>

      <div class="col-md-3">
        <div class="form-group">
          <CustomLabel
            required
            text="Modalidade da turma"
            help="Selecione a modalidade da turma."
          />

          <CustomSelect
            v-model="localOfferClass.optional_fields.modality"
            :options="modalityOptions"
            required
            :disabled="isReadonly"
            :has-error="formErrors.modality.hasError"
            :error-message="formErrors.modality.message"
            @validate="validateForm()"
          />
        </div>
      </div>

      <div class="col-md-3" v-if="localOfferClass.enrol !== 'offer_audience'">
        <div
          class="form-group"
          :class="{
            disabled:
              !localOfferClass.optional_fields.enableenrolperiod || isReadonly,
          }"
        >
          <CustomLabel
            :required="localOfferClass.optional_fields.enableenrolperiod"
            id="enrolperiod"
            text="Duração da matrícula"
            :help="
              'Insira um período em dias para a duração da matricula dos alunos, se houver, na turma. Exemplo: 15 <br><br>' +
              (maxEnrolPeriod
                ? ` O valor máximo permitido é de ${maxEnrolPeriod} dias, que corresponde ao período entre as datas de início e fim da turma.`
                : '')
            "
          />
          <CustomInput
            v-model="localOfferClass.optional_fields.enrolperiod"
            type="number"
            id="enrolperiod"
            :disabled="
              !localOfferClass.optional_fields.enableenrolperiod || isReadonly
            "
            required
            :has-error="formErrors.enrolperiod.hasError"
            :error-message="formErrors.enrolperiod.message"
            :max="maxEnrolPeriod"
            @validate="validateForm()"
          />
        </div>
      </div>
      <div
        class="col-md-3 ml-md-n3 mt-n3 mt-md-0"
        v-if="localOfferClass.enrol !== 'offer_audience'"
      >
        <div class="form-group">
          <CustomLabel text="&nbsp" className="d-none d-md-block" />
          <CustomCheckbox
            v-model="localOfferClass.optional_fields.enableenrolperiod"
            id="enableEnrolPeriod"
            label="Habilitar duração da matrícula"
            :disabled="shouldDisableEnrolPeriod || isReadonly"
            :title="
              shouldDisableEnrolPeriod
                ? 'Não é possível habilitar duração da matrícula para turmas de um dia (data início = data fim)'
                : ''
            "
          />
        </div>
      </div>
    </div>

    <div class="row" v-if="localOfferClass.enrol === 'offer_dealership'">
      <div class="col-md-3" v-if="localOfferClass.enrol === 'offer_dealership'">
        <div class="form-group">
          <CustomLabel
            required
            text="Tipo de custo"
            help="Selecione como será calculado o custo da turma: <br><b>Individual</b>: O custo é calculado por aluno. <br><b>Coletivo</b>: O custo é calculado para a turma como um todo, independentemente do número de alunos."
          />

          <CustomSelect
            v-model="localOfferClass.optional_fields.cost_type"
            :options="costTypeOptions"
            required
            :disabled="
              localOfferClass.optional_fields.modality === 'web' || isReadonly
            "
            :has-error="formErrors.cost_type.hasError"
            :error-message="formErrors.cost_type.message"
            @validate="validateForm()"
          />
        </div>
      </div>

      <div class="col-md-3" v-if="localOfferClass.enrol === 'offer_dealership'">
        <div class="form-group">
          <CustomLabel
            id="enrolperiod"
            :text="
              localOfferClass.optional_fields.cost_type === 'individual'
                ? 'Valor por aluno'
                : 'Valor da turma'
            "
            help="Insira o valor caso a modalidade seja: “Presencial“, “Virtual” ou “Blended."
          />
          <CustomInput
            v-model="localOfferClass.optional_fields.cost_value"
            type="money"
            :mask="{
              mask: Number,
              thousandsSeparator: '.',
              scale: 2,
              radix: ',',
            }"
            id="cost_value"
            :disabled="
              localOfferClass.optional_fields.modality === 'web' || isReadonly
            "
            required
            :has-error="formErrors.cost_value.hasError"
            :error-message="formErrors.cost_value.message"
            @validate="validateForm()"
          />
        </div>
      </div>

      <div class="col-md-3" v-if="localOfferClass.enrol === 'offer_dealership'">
        <div class="form-group">
          <CustomLabel text="Lista de espera" />
          <CustomCheckbox
            v-model="localOfferClass.optional_fields.enable_waitlist"
            id="enable_waitlist"
            label="Habilitar lista de espera"
            :disabled="isReadonly"
          />
        </div>
      </div>
    </div>

    <div class="row" v-if="localOfferClass.enrol === 'offer_dealership'">
      <div class="col-md-3" v-if="localOfferClass.enrol === 'offer_dealership'">
        <div
          class="form-group"
          :class="{
            disabled: !localOfferClass.optional_fields.enable_enrol_cancel,
          }"
        >
          <CustomLabel
            :required="localOfferClass.optional_fields.enable_enrol_cancel"
            text="Prazo de cancelamento"
            help="Ao habilitar esta opção, o campo 'Prazo de Cancelamento' deverá ser preenchido com a quantidade de dias permitidos para o cancelamento da inscrição. Por exemplo: ao definir '5', o Gestor poderá cancelar a inscrição do usuário em até 5 dias úteis antes do início da turma."
          />

          <CustomInput
            v-model="localOfferClass.optional_fields.enrol_cancel_deadline"
            type="number"
            :disabled="
              !localOfferClass.optional_fields.enable_enrol_cancel || isReadonly
            "
            :has-error="formErrors.enrol_cancel_deadline.hasError"
            :error-message="formErrors.enrol_cancel_deadline.message"
            @validate="validateForm()"
          />
        </div>
      </div>

      <div
        class="col-md-3 ml-md-n3 mt-n3 mt-md-0"
        v-if="localOfferClass.enrol === 'offer_dealership'"
      >
        <div class="form-group">
          <CustomLabel text="&nbsp" className="d-none d-md-block" />
          <CustomCheckbox
            v-model="localOfferClass.optional_fields.enable_enrol_cancel"
            id="enable_enrol_cancel"
            label="Habilitar cancelamento de inscrição"
            :disabled="isReadonly"
          />
        </div>
      </div>

      <div class="col-md-3" v-if="localOfferClass.enrol === 'offer_dealership'">
        <div
          class="form-group"
          :class="{
            disabled: !localOfferClass.optional_fields.enable_enrol_replace,
          }"
        >
          <CustomLabel
            :required="localOfferClass.optional_fields.enable_enrol_replace"
            text="Prazo de substituição"
            help="Ao habilitar esta opção, o campo 'Prazo de Substituição' deverá ser preenchido com a quantidade de dias permitidos para a substituição da inscrição. Por exemplo: ao definir '5', o Gestor poderá substituir a inscrição do usuário em até 5 dias úteis antes do início da turma."
          />

          <CustomInput
            v-model="localOfferClass.optional_fields.enrol_replace_deadline"
            type="number"
            :disabled="
              !localOfferClass.optional_fields.enable_enrol_replace ||
              isReadonly
            "
            :has-error="formErrors.enrol_replace_deadline.hasError"
            :error-message="formErrors.enrol_replace_deadline.message"
            @validate="validateForm()"
          />
        </div>
      </div>

      <div
        class="col-md-3 ml-md-n3 mt-n3 mt-md-0"
        v-if="localOfferClass.enrol === 'offer_dealership'"
      >
        <div class="form-group">
          <CustomLabel text="&nbsp" className="d-none d-md-block" />
          <CustomCheckbox
            v-model="localOfferClass.optional_fields.enable_enrol_replace"
            id="enable_enrol_replace"
            label="Habilitar substituição de inscrição"
            :disabled="isReadonly"
          />
        </div>
      </div>
    </div>

    <!-- Habilitar prorrogação -->
    <div class="row mb-n3" v-if="localOfferClass.enrol !== 'offer_audience'">
      <div class="col-md-12">
        <div
          class="form-group"
          :class="{
            disabled: !localOfferClass.optional_fields.enableenrolperiod,
            'dependent-field': true,
          }"
        >
          <CustomCheckbox
            v-model="localOfferClass.optional_fields.enableextension"
            id="enableextension"
            label="Habilitar prorrogação de matrícula"
            :disabled="
              !localOfferClass.optional_fields.enableenrolperiod || isReadonly
            "
            help="A prorrogação estende a duração da matrícula do usuário, permitindo sua permanência na turma enquanto ela estiver ativa. No entanto, não redefine seu progresso, garantindo que ele retome o curso de onde parou.<br><br> Nota: A prorrogação só pode ser habilitada quando o Prazo de conclusão da turma estiver habilitado."
            :title="
              !localOfferClass.optional_fields.enableenrolperiod
                ? 'É necessário habilitar o duração da matrícula primeiro'
                : ''
            "
            @validate="validateForm()"
          />
        </div>
      </div>
    </div>

    <!-- Dias de prorrogação, Dias antes do fim para exibir o botão de prorrogação, Quantidade máxima de prorrogações permitidas -->
    <div class="row" v-if="localOfferClass.enrol !== 'offer_audience'">
      <div class="col-md-4">
        <div
          class="form-group"
          :class="{
            disabled:
              !localOfferClass.optional_fields.enableextension ||
              !localOfferClass.optional_fields.enableenrolperiod,
            'dependent-field': true,
          }"
        >
          <CustomLabel
            :required="localOfferClass.optional_fields.enableextension"
            text="Dias adicionais na matrícula"
            help="Insira um período em dias para prorrogar a matricula dos alunos da turma. Exemplo: 3."
          />
          <CustomInput
            v-model="localOfferClass.optional_fields.extensionperiod"
            type="number"
            :disabled="
              !localOfferClass.optional_fields.enableextension ||
              !localOfferClass.optional_fields.enableenrolperiod ||
              isReadonly
            "
            required
            :has-error="formErrors.extensionperiod.hasError"
            :error-message="formErrors.extensionperiod.message"
            @validate="validateForm()"
          />
        </div>
      </div>
      <div class="col-md-4">
        <div
          class="form-group"
          :class="{
            disabled:
              !localOfferClass.optional_fields.enableextension ||
              !localOfferClass.optional_fields.enableenrolperiod,
            'dependent-field': true,
          }"
        >
          <CustomLabel
            :required="localOfferClass.optional_fields.enableextension"
            text="Dias antes do fim para exibir o botão de prorrogação"
          />
          <CustomInput
            v-model="localOfferClass.optional_fields.extensiondaysavailable"
            type="number"
            :disabled="
              !localOfferClass.optional_fields.enableextension ||
              !localOfferClass.optional_fields.enableenrolperiod ||
              isReadonly
            "
            required
            :has-error="formErrors.extensiondaysavailable.hasError"
            :error-message="formErrors.extensiondaysavailable.message"
            @validate="validateForm()"
          />
        </div>
      </div>
      <div class="col-md-4">
        <div
          class="form-group"
          :class="{
            disabled:
              !localOfferClass.optional_fields.enableextension ||
              !localOfferClass.optional_fields.enableenrolperiod,
            'dependent-field': true,
          }"
        >
          <CustomLabel
            :required="localOfferClass.optional_fields.enableextension"
            text="Quantidade máxima de prorrogações permitidas"
          />
          <CustomInput
            v-model="localOfferClass.optional_fields.extensionmaxrequests"
            type="number"
            :disabled="
              !localOfferClass.optional_fields.enableextension ||
              !localOfferClass.optional_fields.enableenrolperiod ||
              isReadonly
            "
            required
            :has-error="formErrors.extensionmaxrequests.hasError"
            :error-message="formErrors.extensionmaxrequests.message"
            @validate="validateForm()"
          />
        </div>
      </div>
    </div>

    <!-- Habilitar rematrícula -->
    <div class="row mb-n3" v-if="localOfferClass.enrol !== 'offer_dealership'">
      <div class="col-md-4">
        <div class="form-group">
          <CustomCheckbox
            v-model="localOfferClass.optional_fields.enablereenrol"
            id="enableReenrol"
            label="Habilitar rematrícula"
            text="Habilitar rematrícula"
            help="Permite que usuários se matriculem novamente na turma após concluírem ou saírem dela."
            :disabled="isReadonly"
          />
        </div>
      </div>
    </div>

    <!-- Status que permite rematrículas -->
    <div class="row" v-if="localOfferClass.enrol !== 'offer_dealership'">
      <div class="col-md-4">
        <div
          class="form-group"
          :class="{ disabled: !localOfferClass.optional_fields.enablereenrol }"
        >
          <CustomLabel
            text="Status que permite rematrículas"
            :required="localOfferClass.optional_fields.enablereenrol"
          />
          <Autocomplete
            v-model="localOfferClass.optional_fields.reenrolmentsituations"
            :items="situationOptions"
            placeholder="Selecione o status..."
            :required="true"
            :disabled="
              !localOfferClass.optional_fields.enablereenrol || isReadonly
            "
            :auto-open="false"
            :has-error="formErrors.reenrolmentsituations.hasError"
            :error-message="formErrors.reenrolmentsituations.message"
            @update:modelValue="validateForm()"
          />
        </div>
      </div>
    </div>

    <!-- Atribuir Instrutor -->
    <div class="row">
      <div class="col-md-4">
        <div class="form-group mb-3">
          <CustomLabel
            text="Atribuir Instrutor"
            help="Ao selecionar usuários para a composição do corpo docente, ele será matriculado na turma com o papel “Professor”."
          />
          <Autocomplete
            class="autocomplete-audiences"
            v-model="localOfferClass.teachers"
            :items="teacherOptions"
            :has-search-icon="true"
            placeholder="Pesquisar..."
            :autoOpen="false"
            :show-all-option="false"
            :keep-open-on-select="true"
            :loading="loadingTeachers"
            :disabled="isReadonly"
            @update:modelValue="validateForm()"
            @search="(search) => debouncedSearchTeachers(search)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { isEqual, debounce } from "lodash";
import {
  getSituationList,
  getCourseRoles,
  getHierarchyDivisions,
  getHierarchySectors,
  getHierarchyGroups,
  getHierarchyDealerships,
} from "@/services/offer";
import { getPotentialTeachers } from "@/services/offer";
import CustomLabel from "@/components/CustomLabel.vue";
import CustomInput from "@/components/CustomInput.vue";
import CustomSelect from "@/components/CustomSelect.vue";
import CustomButton from "@/components/CustomButton.vue";
import PageHeader from "@/components/PageHeader.vue";
import BackButton from "@/components/BackButton.vue";
import Autocomplete from "@/components/Autocomplete.vue";
import TextEditor from "@/components/TextEditor.vue";
import CustomCheckbox from "@/components/CustomCheckbox.vue";
import FilterRow from "@/components/FilterRow.vue";
import FilterGroup from "@/components/FilterGroup.vue";
import Toast from "@/components/Toast.vue";
import HelpIcon from "@/components/HelpIcon.vue";
import FilterTag from "@/components/FilterTag.vue";
import FilterTags from "@/components/FilterTags.vue";

import ToastMessages from "@/mixins/toastMessages";
import { valid } from "semver";

export default {
  name: "Form",

  mixins: [ToastMessages],

  components: {
    CustomLabel,
    CustomInput,
    CustomSelect,
    CustomButton,
    PageHeader,
    BackButton,
    Autocomplete,
    TextEditor,
    CustomCheckbox,
    FilterRow,
    FilterGroup,
    Toast,
    HelpIcon,
    FilterTag,
    FilterTags,
  },

  props: {
    offerCourse: {
      type: Object,
      required: true,
    },

    offerClass: {
      type: Object,
      required: true,
    },
    isEditing: {
      type: Boolean,
      required: true,
    },
    isReadonly: {
      type: Boolean,
      default: false,
    },
  },

  emits: ["update:offerClass", "validate"],

  data() {
    return {
      loading: false,
      loadingTeachers: false,

      // Dados da turma
      localOfferClass: {},

      divisionOptions: [],
      sectorOptions: [],
      groupOptions: [],
      dealershipOptions: [],

      roleOptions: [],
      teacherOptions: [],
      modalityOptions: [],
      situationOptions: [],
      costTypeOptions: [],

      formErrors: {
        enrol: {
          hasError: false,
          message: "Método de inscrição é obrigatório",
        },
        classname: { hasError: false, message: "Nome da turma é obrigatório" },
        startdate: { hasError: false, message: "Data de início é obrigatória" },
        roleid: { hasError: false, message: "Perfil padrão é obrigatório" },
        modality: { hasError: false, message: "Modalidade é obrigatório" },
        enddate: {
          hasError: false,
          message: "Data fim da turma é obrigatória quando habilitada",
        },
        preenrolmentstartdate: {
          hasError: false,
          message:
            "Data início de pré-inscrição é obrigatória quando habilitada",
        },
        preenrolmentenddate: {
          hasError: false,
          message: "Data fim de pré-inscrição é obrigatória quando habilitada",
        },
        enrolperiod: {
          hasError: false,
          message:
            "Prazo de conclusão é obrigatório quando habilitado e não pode exceder o período entre as datas de início e fim da turma",
        },
        extensionperiod: {
          hasError: false,
          message: "Dias para prorrogação é obrigatório quando habilitada",
        },
        extensiondaysavailable: {
          hasError: false,
          message: "Dias antes do término é obrigatório quando habilitada",
        },
        extensionmaxrequests: {
          hasError: false,
          message: "Máximo de solicitações é obrigatório quando habilitada",
        },
        reenrolmentsituations: {
          hasError: false,
          message:
            "É necessário selecionar um status quando a rematrícula estiver habilitada",
        },
        minusers: {
          hasError: false,
          message: "Mínimo de usuários deve ser maior ou igual a zero",
        },
        maxusers: {
          hasError: false,
          message: "Máximo de usuários deve ser maior ou igual a zero",
        },
        max_users_company: {
          hasError: false,
          message:
            "Número máximo de inscrições por concessionária deve ser maior ou igual a zero",
        },
        cost_value: {
          hasError: false,
          message: "Valor da turma é obrigatório",
        },
        cost_type: {
          hasError: false,
          message: "Tipo de custo é obrigatório",
        },
        enrol_cancel_deadline: {
          hasError: false,
          message: "Prazo de cancelamento de inscrição é obrigatório",
        },
        enrol_replace_deadline: {
          hasError: false,
          message: "Prazo de substituição de inscrição é obrigatório",
        },
      },
    };
  },

  async created() {
    await this.getInitialData();

    this.debouncedSearchTeachers = debounce((searchString) => {
      this.fetchPotentialTeachers(searchString);
    }, 300);
  },

  computed: {
    /**
     * Calculetes the maximum value for the enrol period based on the start and end dates of the class.
     */
    maxEnrolPeriod() {
      // Só calcula se ambas as datas estiverem preenchidas e a data fim estiver habilitada
      if (
        this.localOfferClass.startdate &&
        this.localOfferClass.optional_fields.enddate &&
        this.localOfferClass.optional_fields.enableenddate
      ) {
        // Calcula a diferença em dias entre as datas
        const daysDifference = this.calculateDaysDifference(
          this.localOfferClass.startdate,
          this.localOfferClass.optional_fields.enddate
        );

        // Retorna a diferença em dias como valor máximo (mínimo 1 dia)
        return daysDifference >= 1 ? daysDifference : 1;
      }

      // Se alguma das condições não for atendida, retorna null (sem limite)
      return null;
    },

    // Verifica se as datas de início e fim são iguais (turma de um dia)
    isOneDayClass() {
      // Só considera turma de um dia se:
      // 1. Data início estiver preenchida
      // 2. Data fim estiver habilitada E preenchida
      // 3. As duas datas forem iguais
      return (
        this.localOfferClass.startdate &&
        this.localOfferClass.optional_fields.enableenddate &&
        this.localOfferClass.optional_fields.enddate &&
        this.localOfferClass.startdate ===
          this.localOfferClass.optional_fields.enddate
      );
    },

    // Verifica se o prazo de conclusão deve estar desabilitado
    shouldDisableEnrolPeriod() {
      // Só desabilita se for realmente uma turma de um dia
      // (ambas as datas preenchidas e iguais)
      return this.isOneDayClass;
    },
  },

  watch: {
    offerClass: {
      handler(newVal) {
        if (!isEqual(newVal, this.localOfferClass)) {
          this.localOfferClass = {
            ...newVal,
          };
        }
      },
      deep: true,
      immediate: true,
    },

    localOfferClass: {
      handler(newVal) {
        if (!isEqual(newVal, this.offerClass)) {
          this.$emit("update:offerClass", newVal);
        }
      },
      deep: true,
    },

    "localOfferClass.optional_fields.enablehirearchyrestriction": function (
      newValue,
      oldValue
    ) {
      if (isEqual(newValue, oldValue)) {
        return;
      }

      if (!newValue) {
        this.localOfferClass.optional_fields = {
          ...this.localOfferClass.optional_fields,
          hirearchyrestrictiondivisions: [],
          hirearchyrestrictionsectors: [],
          hirearchyrestrictiongroups: [],
          hirearchyrestrictiondealerships: [],
        };
      }
    },

    "localOfferClass.optional_fields.hirearchyrestrictiondivisions": function (
      newValue,
      oldValue
    ) {
      if (isEqual(newValue, oldValue)) {
        return;
      }

      if (!newValue.length) {
        this.localOfferClass.optional_fields.hirearchyrestrictionsectors = [];
        this.localOfferClass.optional_fields.hirearchyrestrictiongroups = [];
        this.localOfferClass.optional_fields.hirearchyrestrictiondealerships =
          [];
        return;
      }

      const divisionids = newValue.map((item) => item.value);

      this.getSectors(divisionids);
    },

    "localOfferClass.optional_fields.hirearchyrestrictionsectors": function (
      newValue,
      oldValue
    ) {
      if (isEqual(newValue, oldValue)) {
        return;
      }

      if (!newValue.length) {
        this.localOfferClass.optional_fields.hirearchyrestrictiongroups = [];
        this.localOfferClass.optional_fields.hirearchyrestrictiondealerships =
          [];
        return;
      }

      const sectorids = newValue.map((item) => item.value);

      this.getGroups(sectorids);
    },

    "localOfferClass.optional_fields.hirearchyrestrictiongroups": function (
      newValue,
      oldValue
    ) {
      if (isEqual(newValue, oldValue)) {
        return;
      }

      if (!newValue.length) {
        this.localOfferClass.optional_fields.hirearchyrestrictiondealerships =
          [];
        return;
      }

      const groupids = newValue.map((item) => item.value);

      this.getDealerships(groupids);
    },

    // Observa mudanças no estado de habilitação do prazo de conclusão
    "localOfferClass.optional_fields.enableenrolperiod": function (newValue) {
      if (!newValue && this.localOfferClass.optional_fields.enableextension) {
        this.localOfferClass.optional_fields.enableextension = false;

        this.showWarningMessage(
          "Prorrogação de matrícula foi desabilitada automaticamente porque o Prazo de conclusão da turma foi desabilitado."
        );
      }
    },

    "localOfferClass.optional_fields.enableenddate": function (newValue) {
      console.log("enableenddate", newValue);
      if (!newValue) {
        this.localOfferClass.startdate = null;
        this.localOfferClass.optional_fields.enddate = null;
      }
    },
    "localOfferClass.startdate": function () {
      // Valida pré-inscrição se estiver habilitada
      if (this.localOfferClass.optional_fields.enablepreenrolment) {
        this.validatePreenrolmentDates();
      }

      // Verifica se deve desabilitar o prazo de conclusão (turma de um dia)
      this.checkAndDisableEnrolPeriodForOneDayClass();
    },
    "localOfferClass.optional_fields.enddate": function () {
      // Valida pré-inscrição se estiver habilitada
      if (
        this.localOfferClass.optional_fields.enablepreenrolment &&
        this.localOfferClass.optional_fields.enableenddate
      ) {
        this.validatePreenrolmentDates();
      }

      // Verifica se deve desabilitar o prazo de conclusão (turma de um dia)
      this.checkAndDisableEnrolPeriodForOneDayClass();
    },

    // Observa mudanças na habilitação da data fim da turma
    "localOfferClass.optional_fields.enableenddate": function (newValue) {
      // Valida pré-inscrição se estiver habilitada
      if (this.localOfferClass.optional_fields.enablepreenrolment) {
        this.validatePreenrolmentDates();
      }

      // Verifica se deve desabilitar o prazo de conclusão (turma de um dia)
      this.checkAndDisableEnrolPeriodForOneDayClass();
    },

    "localOfferClass.optional_fields.preenrolmentstartdate": function () {
      if (this.localOfferClass.optional_fields.enablepreenrolment) {
        this.validatePreenrolmentDates();
      }
    },
    "localOfferClass.optional_fields.preenrolmentenddate": function () {
      if (this.localOfferClass.optional_fields.enablepreenrolment) {
        this.validatePreenrolmentDates();
      }
    },

    "localOfferClass.optional_fields.enablereenrol": function () {
      this.isValidField("reenrolmentsituations");
    },
  },

  methods: {
    /**
     * Gets initial data for the form
     */
    async getInitialData() {
      try {
        this.loading = true;

        await this.setEnrolmentMethod();

        await this.getRoles();

        await this.getModalities();

        await this.getCostTypes();

        await this.getSituations();

        await this.getHierarchyRestrictionData();
      } catch (error) {
        this.showErrorMessage("Alguns dados não puderam ser carregados.");
      } finally {
        this.loading = false;
      }
    },

    /**
     * Carrega papéis disponíveis
     */
    async getRoles() {
      const response = await getCourseRoles(this.offerCourse.id);

      this.roleOptions = response
        .map((role) => ({
          value: role.id,
          label: role.name,
        }))
        .sort((a, b) => a.label.localeCompare(b.label));

      if (!this.localOfferClass.optional_fields.roleid) {
        const studentRole = this.roleOptions.find((role) => role.value === 5);

        this.localOfferClass.optional_fields.roleid =
          studentRole?.value ?? this.roleOptions[0].value;
      }
    },

    /**
     * Loads available modalities for the class and sets the default modality.
     */
    async getModalities() {
      this.modalityOptions = [
        { value: "presencial", label: "Presencial" },
        { value: "web", label: "WEB" },
        { value: "virtual", label: "Virtual" },
        { value: "blended", label: "Blended" },
      ];

      if (!this.localOfferClass.optional_fields.modality) {
        this.localOfferClass.optional_fields.modality =
          this.modalityOptions[0].value;
      }
    },

    /**
     * Loads available cost types for the class and sets the default cost type.
     */
    async getCostTypes() {
      this.costTypeOptions = [
        { value: "individual", label: "Individual" },
        { value: "coletivo", label: "Coletivo" },
      ];
      if (!this.localOfferClass.optional_fields.cost_type) {
        this.localOfferClass.optional_fields.cost_type =
          this.costTypeOptions[0].value;
      }
    },

    /**
     * Carrega situações de matrícula
     */
    async getSituations() {
      const response = await getSituationList();

      this.situationOptions = response.map((situation) => ({
        value: situation.id,
        label: situation.name,
      }));
    },

    /**
     * Sets the enrolment method for the class on create
     */
    async setEnrolmentMethod() {
      try {
        if (this.isEditing) {
          return;
        }

        const enrol = this.$route.query.enrolMethod;

        if (!enrol) throw new Error("Método de inscrição não informado");

        this.localOfferClass.enrol = enrol;
      } catch (error) {
        this.showErrorMessage(error);
      }
    },

    async getHierarchyRestrictionData() {
      await this.getDivisions();

      if (!this.localOfferClass.optional_fields.enablehirearchyrestriction) {
        return;
      }

      if (
        this.localOfferClass.optional_fields.hirearchyrestrictiondivisions
          .length
      ) {
        await this.getSectors();
      }

      if (
        this.localOfferClass.optional_fields.hirearchyrestrictionsectors.length
      ) {
        await this.getGroups();
      }

      if (
        this.localOfferClass.optional_fields.hirearchyrestrictiongroups.length
      ) {
        await this.getDealerships();
      }
    },

    /**
     * Loads available divisions
     *
     * @returns {Promise<void>}
     */
    async getDivisions() {
      try {
        const response = await getHierarchyDivisions();

        this.divisionOptions = response.map((division) => ({
          value: division.id,
          label: division.name,
        }));
      } catch (error) {
        this.showErrorMessage(error);
      }
    },

    /**
     * Loads available sectors
     * @param {Array<number>} divisionIds
     * @returns {Promise<void>}
     */
    async getSectors(divisionIds) {
      try {
        if (!divisionIds) {
          divisionIds =
            this.localOfferClass.optional_fields.hirearchyrestrictiondivisions.map(
              (item) => item.value
            );
        }

        const response = await getHierarchySectors(divisionIds);

        this.sectorOptions = response.map((sector) => ({
          value: sector.id,
          label: sector.name,
        }));
      } catch (error) {
        this.showErrorMessage(error);
      }
    },

    /**
     * Loads available groups
     * @param {Array<number>} sectorIds
     * @returns {Promise<void>}
     */

    async getGroups(sectorIds) {
      try {
        if (!sectorIds) {
          sectorIds =
            this.localOfferClass.optional_fields.hirearchyrestrictionsectors.map(
              (item) => item.value
            );
        }

        const response = await getHierarchyGroups(sectorIds);

        this.groupOptions = response.map((group) => ({
          value: group.id,
          label: group.name,
        }));
      } catch (error) {
        this.showErrorMessage(error);
      }
    },

    /**
     * Loads available dealerships
     * @param {Array<number>} groupIds
     * @returns {Promise<void>}
     */
    async getDealerships(groupIds) {
      try {
        if (!groupIds) {
          groupIds =
            this.localOfferClass.optional_fields.hirearchyrestrictiongroups.map(
              (item) => item.value
            );
        }

        const response = await getHierarchyDealerships(groupIds);

        this.dealershipOptions = response.map((dealership) => ({
          value: dealership.id,
          label: dealership.name,
        }));
      } catch (error) {
        this.showErrorMessage(error);
      }
    },

    /**
     * Searches for potential teachers
     * @param {string} search
     */
    async fetchPotentialTeachers(search) {
      try {
        if (search.length < 3) return;

        this.loadingTeachers = true;

        let selectedTeacherIds =
          this.localOfferClass.teachers.map((teacher) => teacher.value) ?? [];

        const response = await getPotentialTeachers(
          this.offerCourse.id,
          this.classId,
          search,
          selectedTeacherIds
        );

        this.teacherOptions = response.map((teacher) => ({
          value: teacher.id,
          label: teacher.fullname,
        }));
      } catch (error) {
        this.showErrorMessage(error);
      } finally {
        this.loadingTeachers = false;
      }
    },

    /**
     * Validates all form fields and emits a validation event.
     * @returns {boolean} If the form is valid.
     */
    validateForm() {
      let isValid = true;

      Object.keys(this.formErrors).forEach((field) => {
        if (!this.isValidField(field)) {
          isValid = false;
        }
      });

      this.$emit("validate", isValid);

      return isValid;
    },

    /**
     * Valida um campo específico
     * @param {string} field Nome do campo a ser validado
     */
    isValidField(field) {
      switch (field) {
        case "classname":
          this.formErrors.classname.hasError = !this.localOfferClass.classname;
          break;
        case "startdate":
          // Verifica se o campo está preenchido
          const startdateHasValue = this.localOfferClass.startdate;

          // Verifica se a data início é maior que a data fim (quando data fim estiver habilitada)
          const startdateAfterEnddate =
            startdateHasValue &&
            this.localOfferClass.optional_fields.enableenddate &&
            this.localOfferClass.optional_fields.enddate &&
            new Date(this.localOfferClass.startdate) >
              new Date(this.localOfferClass.optional_fields.enddate);

          if (!startdateHasValue) {
            this.formErrors.startdate.message = "Data de início é obrigatória";
            this.formErrors.startdate.hasError = true;
          } else if (startdateAfterEnddate) {
            this.formErrors.startdate.message =
              "Data de início deve ser igual ou anterior à data fim da turma";
            this.formErrors.startdate.hasError = true;
          } else {
            this.formErrors.startdate.message = "Data de início é obrigatória";
            this.formErrors.startdate.hasError = false;
          }
          break;
        case "roleid":
          this.formErrors.roleid.hasError =
            !this.localOfferClass.optional_fields.roleid;
          break;
        case "enddate":
          // Verifica se o campo está habilitado e se tem valor
          const enddateEnabled =
            this.localOfferClass.optional_fields.enableenddate;
          const enddateHasValue = this.localOfferClass.optional_fields.enddate;

          // Verifica se a data fim é menor que a data início
          const enddateBeforeStartdate =
            enddateEnabled &&
            enddateHasValue &&
            this.localOfferClass.startdate &&
            new Date(this.localOfferClass.optional_fields.enddate) <
              new Date(this.localOfferClass.startdate);

          if (enddateEnabled && !enddateHasValue) {
            this.formErrors.enddate.message =
              "Data fim da turma é obrigatória quando habilitada";
            this.formErrors.enddate.hasError = true;
          } else if (enddateBeforeStartdate) {
            this.formErrors.enddate.message =
              "Data fim da turma deve ser igual ou posterior à data de início";
            this.formErrors.enddate.hasError = true;
          } else {
            this.formErrors.enddate.message =
              "Data fim da turma é obrigatória quando habilitada";
            this.formErrors.enddate.hasError = false;
          }
          break;
        case "preenrolmentstartdate":
          this.formErrors.preenrolmentstartdate.hasError =
            this.localOfferClass.optional_fields.enablepreenrolment &&
            !this.localOfferClass.optional_fields.preenrolmentstartdate;
          this.validatePreenrolmentDates();
          break;
        case "preenrolmentenddate":
          this.formErrors.preenrolmentenddate.hasError =
            this.localOfferClass.optional_fields.enablepreenrolment &&
            !this.localOfferClass.optional_fields.preenrolmentenddate;
          this.validatePreenrolmentDates();
          break;
        case "enrolperiod":
          // Verifica se o campo está habilitado e se tem valor
          const isEnabled =
            this.localOfferClass.optional_fields.enableenrolperiod;
          const hasValue =
            this.localOfferClass.optional_fields.enrolperiod !== null &&
            this.localOfferClass.optional_fields.enrolperiod !== undefined &&
            this.localOfferClass.optional_fields.enrolperiod !== "";

          // Verifica se o valor excede o máximo permitido
          const exceedsMaximum =
            this.maxEnrolPeriod !== null &&
            hasValue &&
            parseInt(this.localOfferClass.optional_fields.enrolperiod) >
              this.maxEnrolPeriod;

          // Define a mensagem de erro apropriada
          if (isEnabled && !hasValue) {
            this.formErrors.enrolperiod.message =
              "Prazo de conclusão é obrigatório quando habilitado";
            this.formErrors.enrolperiod.hasError = true;
          } else if (isEnabled && exceedsMaximum) {
            this.formErrors.enrolperiod.message = `Prazo de conclusão não pode exceder ${this.maxEnrolPeriod} dias (período entre as datas de início e fim da turma)`;
            this.formErrors.enrolperiod.hasError = true;
          } else {
            this.formErrors.enrolperiod.message =
              "Prazo de conclusão é obrigatório quando habilitado e não pode exceder o período entre as datas de início e fim da turma";
            this.formErrors.enrolperiod.hasError = false;
          }
          break;
        case "enableextension":
          this.validateExtensionPeriod();
          this.validateExtensionDaysAvailable();
          this.validateExtensionMaxRequests();
          break;
        case "extensionperiod":
          this.validateExtensionPeriod();
          break;
        case "extensiondaysavailable":
          this.validateExtensionDaysAvailable();
          break;
        case "extensionmaxrequests":
          this.validateExtensionMaxRequests();
          break;
        case "reenrolmentsituations":
          this.validateReenrolmentSituations();
          break;
        case "minusers":
          this.validateMinUsers();
          break;
        case "maxusers":
          this.validateMaxUsers();
          this.validateMaxUsersByDealership();
          break;
        case "max_users_company":
          this.validateMaxUsersByDealership();
          break;
      }

      return !this.formErrors[field].hasError;
    },

    mapToOptions(array) {
      return array.map((item) => ({ value: item, label: "" }));
    },

    mapToValues(array) {
      return array.map((item) => item.value);
    },

    /**
     * Calcula a diferença em dias entre duas datas
     * @param {string} startDate - Data de início no formato YYYY-MM-DD
     * @param {string} endDate - Data de fim no formato YYYY-MM-DD
     * @returns {number} - Diferença em dias entre as datas
     */
    calculateDaysDifference(startDate, endDate) {
      // Verifica se as datas são válidas
      if (!startDate || !endDate) {
        return 0;
      }

      // Converte as strings de data para objetos Date
      const start = new Date(startDate);
      const end = new Date(endDate);

      // Verifica se as datas são válidas
      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return 0;
      }

      // Se as datas são iguais, retorna 1 (um dia de prazo)
      if (start.getTime() === end.getTime()) {
        return 1;
      }

      // Calcula a diferença em milissegundos
      const diffTime = Math.abs(end - start);

      // Converte para dias e arredonda para cima
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      return diffDays;
    },

    /**
     * Verifica se deve desabilitar o prazo de conclusão para turmas de um dia
     */
    checkAndDisableEnrolPeriodForOneDayClass() {
      // Se as datas são iguais (turma de um dia), desabilita o prazo de conclusão
      if (
        this.isOneDayClass &&
        this.localOfferClass.optional_fields.enableenrolperiod
      ) {
        this.localOfferClass.optional_fields.enableenrolperiod = false;
        this.localOfferClass.optional_fields.enrolperiod = null;

        // Também desabilita a prorrogação se estiver habilitada
        if (this.localOfferClass.optional_fields.enableextension) {
          this.localOfferClass.optional_fields.enableextension = false;
          this.localOfferClass.optional_fields.extensionperiod = null;
          this.localOfferClass.optional_fields.extensiondaysavailable = null;
          this.localOfferClass.optional_fields.extensionmaxrequests = null;
        }

        // Exibe mensagem informativa
        this.showWarningMessage(
          "Prazo de conclusão foi desabilitado automaticamente porque a turma tem duração de apenas um dia (data início = data fim)."
        );
      }
    },

    validatePreenrolmentDates() {
      let isValid = true;

      // Resetar erros
      this.formErrors.preenrolmentstartdate.hasError = false;
      this.formErrors.preenrolmentenddate.hasError = false;

      // Só validar se pré-inscrição estiver habilitada
      if (this.localOfferClass.optional_fields.enablepreenrolment) {
        const startDate = this.localOfferClass.startdate;
        const endDate = this.localOfferClass.optional_fields.enableenddate
          ? this.localOfferClass.optional_fields.enddate
          : null;
        const preStart =
          this.localOfferClass.optional_fields.preenrolmentstartdate;
        const preEnd = this.localOfferClass.optional_fields.preenrolmentenddate;

        const courseStartDate = this.offerCourse.startdate;
        const courseEndDate = this.offerCourse.enddate;

        // Validar se as datas foram preenchidas
        if (!preStart) {
          this.formErrors.preenrolmentstartdate.hasError = true;
          this.formErrors.preenrolmentstartdate.message =
            "Data início de pré-inscrição é obrigatória";
          isValid = false;
        }

        if (!preEnd) {
          this.formErrors.preenrolmentenddate.hasError = true;
          this.formErrors.preenrolmentenddate.message =
            "Data fim de pré-inscrição é obrigatória";
          isValid = false;
        }

        // Validar que preenrolmentenddate > preenrolmentstartdate
        if (new Date(preEnd) < new Date(preStart)) {
          this.formErrors.preenrolmentenddate.hasError = true;
          this.formErrors.preenrolmentenddate.message =
            "Data fim deve ser posterior à data início";
          isValid = false;
        }

        // Validar que preenrolmentstartdate > startdate
        if (new Date(preStart) > new Date(startDate)) {
          this.formErrors.preenrolmentstartdate.hasError = true;
          this.formErrors.preenrolmentstartdate.message =
            "Data início deve ser igual ou anterior à data início da turma";
          isValid = false;
        }

        // Validar que preenrolmentstartdate < courseStartdate
        if (new Date(preStart) < new Date(courseStartDate)) {
          this.formErrors.preenrolmentstartdate.hasError = true;
          this.formErrors.preenrolmentstartdate.message =
            "Data início deve ser igual ou posterior à data início do curso";
          isValid = false;
        }

        // Validar que preenrolmentstartdate > courseEnddate
        if (courseEndDate && new Date(preStart) > new Date(courseEndDate)) {
          this.formErrors.preenrolmentstartdate.hasError = true;
          this.formErrors.preenrolmentstartdate.message =
            "Data de início deve ser igual ou anterior à data fim do curso";
          isValid = false;
        }
        // Se enddate estiver habilitada, validar que preenrolmentenddate <= enddate
        if (endDate && new Date(preEnd) >= new Date(endDate)) {
          this.formErrors.preenrolmentenddate.hasError = true;
          this.formErrors.preenrolmentenddate.message =
            "Data fim deve ser anterior à data fim da turma";
          isValid = false;
        }

        // Validar que preenrolmentenddate > courseEnddate
        if (courseEndDate && new Date(preEnd) > new Date(courseEndDate)) {
          this.formErrors.preenrolmentenddate.hasError = true;
          this.formErrors.preenrolmentenddate.message =
            "Data fim deve ser igual ou anterior à data fim do curso";
          isValid = false;
        }
      }
      return isValid;
    },

    /**
     * Valida o campo mínimo de usuários
     */
    validateMinUsers() {
      const minUsers = parseInt(this.localOfferClass.optional_fields.minusers);
      const maxUsers = parseInt(this.localOfferClass.optional_fields.maxusers);

      this.formErrors.minusers.hasError = false;

      if (minUsers === 0) {
        return true;
      }

      if (maxUsers > 0) {
        if (minUsers > maxUsers) {
          this.formErrors.minusers.message =
            "Mínimo de usuários inscritos deve ser menor ou igual que o máximo de usuários inscritos";
          this.formErrors.minusers.hasError = true;
          return false;
        }
      }

      return true;
    },

    /**
     * Valida o campo máximo de usuários
     */
    validateMaxUsers() {
      const minUsers = parseInt(this.localOfferClass.optional_fields.minusers);
      const maxUsers = parseInt(this.localOfferClass.optional_fields.maxusers);

      this.formErrors.maxusers.hasError = false;

      if (maxUsers === 0) {
        return true;
      }

      if (minUsers > 0) {
        if (maxUsers < minUsers) {
          this.formErrors.maxusers.message =
            "Máximo de usuários inscritos deve ser maior ou igual que o mínimo de usuários inscritos";
          this.formErrors.maxusers.hasError = true;
          return false;
        }
      }

      return true;
    },

    /**
     * Validates the maximum number of users by dealership
     */
    validateMaxUsersByDealership() {
      const maxUsers = parseInt(this.localOfferClass.optional_fields.maxusers);
      const maxUsersCompany = parseInt(
        this.localOfferClass.optional_fields.max_users_company
      );

      this.formErrors.max_users_company.hasError = false;

      if (maxUsers === 0 || maxUsersCompany === 0) {
        return true;
      }

      if (maxUsersCompany > maxUsers) {
        this.formErrors.max_users_company.message =
          "Número máximo de inscrições por concessionária deve ser menor ou igual ao máximo de inscrições";
        this.formErrors.max_users_company.hasError = true;
        return false;
      }
    },

    validateExtensionPeriod() {
      this.formErrors.extensionperiod.hasError =
        this.localOfferClass.optional_fields.enableextension &&
        this.localOfferClass.optional_fields.enableenrolperiod &&
        !this.localOfferClass.optional_fields.extensionperiod;
    },

    validateExtensionDaysAvailable() {
      this.formErrors.extensiondaysavailable.hasError =
        this.localOfferClass.optional_fields.enableextension &&
        this.localOfferClass.optional_fields.enableenrolperiod &&
        !this.localOfferClass.optional_fields.extensiondaysavailable;
    },

    validateExtensionMaxRequests() {
      this.formErrors.extensionmaxrequests.hasError =
        this.localOfferClass.optional_fields.enableextension &&
        this.localOfferClass.optional_fields.enableenrolperiod &&
        !this.localOfferClass.optional_fields.extensionmaxrequests;
    },

    validateReenrolmentSituations() {
      this.formErrors.reenrolmentsituations.hasError =
        this.localOfferClass.optional_fields.enablereenrol &&
        !this.localOfferClass.optional_fields.reenrolmentsituations.length;
    },

    /**
     * Volta para tela da oferta
     */
    navigateToBack() {
      if (this.offerCourse.offerid) {
        this.router.push({
          name: "offer.edit",
          params: { id: this.offerCourse.offerid },
        });
      } else {
        this.router.push({ name: "offer.index" });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.text-editor-container {
  max-width: 100%;
}
</style>
