<template>
  <div class="custom-input-container" :style="customWidth">
    <CustomLabel v-if="label" :text="label" />
    <div
      class="input-wrapper"
      :class="{ 'with-icon': hasSearchIcon || isDateType }"
    >
      <!-- Se tiver mask, usa IMaskInput -->
      <IMaskInput
        v-if="mask"
        :id="id"
        :mask="mask"
        :unmask="unmask"
        :placeholder="placeholder"
        :value="String(modelValue)"
        :disabled="disabled"
        radix="."
        class="form-control"
        :class="{ 'is-invalid': hasError }"
        @accept="onMaskAccept"
        @blur="handleBlur"
      />

      <!-- Caso contrário usa input normal -->
      <input
        v-else
        :type="type"
        :placeholder="placeholder"
        :value="modelValue"
        @input="handleInput"
        @blur="handleBlur"
        :disabled="disabled"
        class="form-control"
        :class="{ 'is-invalid': hasError }"
        :min="isNumberType ? 0 : null"
        :max="max"
        :id="id"
      />

      <div v-if="hasSearchIcon" class="search-icon">
        <i class="fas fa-search"></i>
      </div>
      <div
        v-if="isDateType"
        class="calendar-icon"
        :class="{ disabled: disabled }"
      >
        <i class="fas fa-calendar-alt"></i>
      </div>
      <div
        v-if="hasError && errorMessage"
        class="form-control-feedback invalid-feedback d-block"
      >
        {{ errorMessage }}
      </div>
    </div>
  </div>
</template>

<script>
import CustomLabel from "@/components/CustomLabel.vue";
import { IMaskComponent } from "vue-imask";

export default {
  name: "CustomInput",

  components: {
    CustomLabel,
    IMaskInput: IMaskComponent,
  },

  props: {
    modelValue: {
      type: [String, Number],
      default: "",
    },
    id: {
      type: String,
      required: false,
      default: "custom-input-" + Math.random().toString(36).substring(2, 9),
    },
    label: String,
    placeholder: {
      type: String,
      default: "Digite aqui...",
    },
    type: {
      type: String,
      default: "text",
    },
    hasSearchIcon: Boolean,
    width: [String, Number],
    disabled: Boolean,
    hasError: Boolean,
    errorMessage: String,
    required: Boolean,
    max: [String, Number],

    /** Nova prop para máscaras */
    mask: {
      type: [String, Object, Array],
      default: null,
    },
    /** Define se o valor emitido será mascarado ou "limpo" */
    unmask: {
      type: [Boolean, String],
      default: false,
    },
  },

  computed: {
    customWidth() {
      return this.width
        ? {
            width:
              typeof this.width === "number" ? `${this.width}px` : this.width,
          }
        : {};
    },
    isDateType() {
      return this.type === "date";
    },
    isNumberType() {
      return this.type === "number";
    },
  },

  methods: {
    handleInput(event) {
      // Se o campo tiver máscara, não usamos handleInput
      if (this.mask) return;

      let value = event.target.value;

      if (this.isNumberType) {
        if (value.includes("-")) {
          value = value.replace(/-/g, "");
        }

        if (value !== "") {
          const numValue = parseFloat(value);
          if (numValue < 0 || isNaN(numValue)) {
            value = "";
          } else if (this.max !== null && numValue > parseFloat(this.max)) {
            value = this.max.toString();
            this.$emit("validate");
          }
        }
      }

      this.$emit("update:modelValue", value);

      if (this.hasError && value) {
        this.$emit("validate");
      }
    },

    onMaskAccept(value) {
      this.$emit("update:modelValue", this.unmask ? value : String(value));

      if (this.hasError && value) {
        this.$emit("validate");
      }
    },

    handleBlur() {
      this.$emit("validate");
    },
  },

  emits: ["update:modelValue", "validate"],
};
</script>

<style lang="scss" scoped>
.custom-input-container {
  width: 100%;
  position: relative;

  @media (max-width: 768px) {
    width: 100% !important;
  }
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  flex-direction: column;
  width: 100%;
  flex: 1;

  .calendar-icon {
    position: absolute;
    right: 10px;
    top: 19px;
    transform: translateY(-50%);
    color: #fff;
    pointer-events: none;

    &.disabled {
      opacity: 0.65;
    }
  }

  .search-icon {
    position: absolute;
    right: 10px;
    top: 19px;
    transform: translateY(-50%);
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: none;
  }

  .form-control {
    &.is-invalid ~ .search-icon,
    &[type="date"].is-invalid ~ .calendar-icon {
      right: 40px;
    }
  }

  input[type="date"] {
    color-scheme: dark;

    /* Esconde o ícone nativo do calendário */
    &::-webkit-calendar-picker-indicator {
      opacity: 0;
    }
  }

  .calendar-icon {
    color: #fff;
    z-index: 1;
  }

  /* Chrome, Safari, Edge, Opera */
  input[type="number"]::-webkit-outer-spin-button,
  input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  /* Firefox */
  input[type="number"] {
    -moz-appearance: textfield;
  }
}
</style>
